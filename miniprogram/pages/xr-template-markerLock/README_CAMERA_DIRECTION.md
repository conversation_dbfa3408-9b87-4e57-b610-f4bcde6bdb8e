# 相机朝向检测功能说明

## 功能概述

新增了基于相机y轴朝向和marker位置的综合判断功能，用于确定相机是否准备进入marker（展项）。

## 核心功能

### `isReadyToEnterMarker(markerPos, cameraPos, cameraQuat)`

这个函数综合考虑了距离和朝向两个因素：

1. **距离检测**: 首先检查相机到marker的距离是否在阈值范围内
2. **朝向检测**: 检查相机的前向方向与marker方向的夹角是否在45度范围内

#### 参数说明
- `markerPos`: marker的位置坐标 `{x, y, z}`
- `cameraPos`: 相机的位置坐标 `{x, y, z}`
- `cameraQuat`: 相机的四元数旋转

#### 返回值
- `boolean`: `true` 表示相机准备进入marker，`false` 表示不满足条件

## 算法原理

1. **距离计算**: 使用现有的 `calculateDistance` 方法计算xz平面上的距离
2. **方向向量计算**: 计算相机到marker的方向向量（归一化）
3. **动态相机前向向量计算**:
   - **问题**: AR系统的坐标变换在不同的VPS定位初始状态下可能不同
   - **解决方案**: 测试多个可能的旋转角度（0°, +90°, -90°, +180°）
   - **选择策略**: 选择与marker方向最接近（点积最大）的旋转角度
   - 这样可以自动适应不同的相机初始朝向
4. **角度判断**: 使用向量点积计算夹角，判断是否小于45度

## 使用场景

- 用户需要大致朝向展项才能触发进入提示
- 避免用户背对展项时误触发
- 提供更自然的交互体验

## 调试信息

函数会在控制台输出调试信息：
```
相机朝向检测 - 到marker角度: 30.5°, 阈值: 45°, 距离: 0.8m
相机Y轴角度 - 原始: 45.0°, 最佳: 135.0°, 偏移: 90.0°
```

这些信息帮助你理解：
- **到marker角度**: 相机朝向与marker方向的夹角
- **原始Y轴角度**: AR系统报告的原始相机y轴旋转角度
- **最佳Y轴角度**: 动态选择的最佳旋转角度
- **偏移**: 从原始角度到最佳角度的偏移量（0°, ±90°, 或180°）

## 改进说明

### 问题背景
原来的实现使用固定的90度补偿，但这在不同的VPS定位初始状态下会产生不一致的结果：
- 如果相机初始朝向anchor方向，再逆时针转90度进行VPS定位，不需要偏移
- 如果相机初始就是逆时针90度朝向进行VPS定位，需要偏移

### 解决方案
新的实现动态测试4个可能的旋转角度（0°, +90°, -90°, +180°），自动选择与marker方向最接近的角度，从而适应不同的相机初始朝向。

## 兼容性

如果相机朝向信息不可用（`this.userPose.quaternion` 为空），系统会自动回退到仅使用距离判断的模式。

## 配置参数

- **距离阈值**: `distanceThreshold = 1` (米)
- **角度阈值**: `45度` (Math.PI/4 弧度)
- **最小距离**: `0.1米` (距离太近时直接允许进入)
